{"version": 2, "dgSpecHash": "Lic98sQEcng=", "success": false, "projectFilePath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\contentmigration\\TNO.Services.ContentMigration.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "libraryId": "Microsoft.Extensions.Logging.Debug"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "libraryId": "TNO.Services"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "libraryId": "Oracle.EntityFrameworkCore"}]}