// 测试HTML内容的标签处理

function parseTagsWithOriginalFormat(text) {
  const tagPattern = /(\s*\[([^\]]+)\])+(?=<\/p>|$)/;
  const match = text.match(tagPattern);
  if (!match) return [];

  const tagBlocks = match[0].match(/\[([^\]]+)\]/g);
  if (!tagBlocks) return [];

  const allTags = [];
  tagBlocks.forEach(block => {
    const tagContent = block.slice(1, -1);
    tagContent.split(',').forEach(tag => {
      const trimmedTag = tag.trim();
      if (trimmedTag) {
        allTags.push({
          tag: trimmedTag.toUpperCase(),
          original: trimmedTag,
        });
      }
    });
  });

  return allTags;
}

function formatTextWithTags(text, tagCodes, originalFormats = {}) {
  const currentText = text ?? '';
  const hasTrailingNewline = currentText?.endsWith('\n') || false;

  console.log(`输入文本: "${currentText}"`);
  console.log(`要添加的标签: [${tagCodes.join(', ')}]`);

  // 删除末尾的标签
  let newText = (currentText || '').replace(/(\s*\[([^\]]*)\])+(?=<\/p>|$)/, '').trimEnd();
  console.log(`删除标签后: "${newText}"`);

  if (tagCodes.length > 0) {
    const formattedTags = tagCodes
      .filter((code) => code && code.trim() !== '')
      .map((code) => originalFormats[code.toUpperCase()] || code);

    if (formattedTags.length > 0) {
      const tagText = `[${formattedTags.join(', ')}]`;

      // handle HTML content and plain text
      if (currentText?.includes('</p>')) {
        console.log('检测到HTML内容，使用HTML处理逻辑');
        newText = newText.replace(/<\/p>\s*$/, '');
        newText = newText ? `${newText} ${tagText}</p>` : `<p>${tagText}</p>`;
      } else {
        console.log('使用纯文本处理逻辑');
        newText = newText ? `${newText} ${tagText}` : tagText;
      }
      console.log(`添加新标签后: "${newText}"`);
    }
  }

  if (hasTrailingNewline) {
    newText += '\n';
  }

  return newText;
}

// 测试HTML内容场景
console.log('=== 测试HTML内容场景 ===');

let htmlText = `<p>Saul Schneider, who lives nearby, came alone, and lit a candle. "This is unspeakable," he said.[a]</p>
<p>"I am reaching out to our friends and neighbours and shopkeepers and nurses to say we love you."</p>
<p>Like others who gathered on the windswept corner of Fraser and 41st, Schneider was processing a sense of shock, disbelief and heartbreak. "This is not Vancouver. Not the Vancouver we truly are."</p>
<p>Schneider said he hopes that people reflect on the deeper issues that may be at the root of what happened.</p>
<p>"This is not just about better barriers at festivals. All levels of government need to pause and reflect on what can be done to address mental health in this province."</p>`;

console.log('\n1. 初始HTML文本:');
console.log(htmlText);

console.log('\n2. 添加第一个标签 MJAG:');
let parsed = parseTagsWithOriginalFormat(htmlText);
console.log('解析到的现有标签:', parsed);
let allTags = [...new Set([...parsed.map(p => p.tag), 'MJAG'])];
htmlText = formatTextWithTags(htmlText, allTags);

console.log('\n3. 添加第二个标签 AGG:');
parsed = parseTagsWithOriginalFormat(htmlText);
console.log('解析到的现有标签:', parsed);
allTags = [...new Set([...parsed.map(p => p.tag), 'AGG'])];
htmlText = formatTextWithTags(htmlText, allTags);

console.log('\n最终HTML结果:');
console.log(htmlText);
