# 标签系统优化需求文档

## 背景

在 TNO 编辑器系统中，内容编辑功能的标签处理存在问题，需要优化标签系统的行为逻辑。

## 核心问题

在 `@/editor` 编辑器中编辑 content 时，点击标签(tag)会出现以下问题：
- 标签系统会处理文本中所有行的标签，包括中间行的标签
- 当用户在标签选择器中添加/删除标签时，系统会影响到文本中间行的标签
- 包括未启用(disabled)的标签和无效标签被错误处理
- 导致数据混乱和用户体验问题

## 具体需求

### 1. 标签过滤需求
- **目标**：只处理已启用(isEnabled=true)的标签
- **行为**：未启用的标签不应该被标签选择器处理或修改

### 2. 标签解析范围限制需求
- **目标**：只处理文本的最后一行标签
- **行为**：前面所有行的标签（包括无效标签）应保持原位不动
- **业务逻辑**："只在最后一行聚合已经激活的标签"

### 3. 标签操作行为需求
- **添加标签**：新标签只添加到文本的最后一行
- **删除标签**：只从最后一行删除标签，不影响前面行的标签
- **标签格式保持**：保持原有标签的格式（大小写、间距等）

### 4. 期望行为示例

**原始文本：**
```
bb[a]
cc [c]
time [ICBC,BC]
```

**当前问题行为：**
- 用户在标签选择器中添加新标签时，系统会处理所有行的标签
- 可能导致 `[a]` 和 `[c]` 被移动或修改

**期望正确行为：**
- `[a]` 和 `[c]` 保持在原位置不被处理
- 只有最后一行的 `[ICBC,BC]` 参与标签系统管理
- 新添加的标签只会出现在最后一行
- 删除标签时只从最后一行删除

**示例场景：**
1. **添加标签 'NEWS'**：
   ```
   bb[a]
   cc [c]
   time [ICBC,BC,NEWS]
   ```

2. **删除标签 'BC'**：
   ```
   bb[a]
   cc [c]
   time [ICBC]
   ```

## 技术实现

### 项目目录结构

```
tno/
├── app/editor/src/features/content/form/
│   ├── components/tags/                    # 标签组件目录
│   │   ├── TagsContext.tsx                # 标签上下文管理（主要修改文件）
│   │   ├── Tags.tsx                       # 标签UI组件
│   │   ├── DraggableTagList.tsx          # 可拖拽标签列表
│   │   ├── hooks/
│   │   │   └── useTagManagement.ts       # 标签管理逻辑（重要修改文件）
│   │   ├── utils/
│   │   │   └── tagParsing.ts             # 标签解析工具（重要修改文件）
│   │   └── types.ts                      # 标签类型定义
│   ├── hooks/
│   │   └── useExtractTags.ts             # 标签提取hook（需修改）
│   ├── ContentForm.tsx                   # 主内容表单
│   ├── ContentStoryForm.tsx              # 内容故事表单
│   └── ContentTranscriptForm.tsx         # 内容转录表单
```

### 核心文件说明

#### 1. `TagsContext.tsx` - 标签上下文管理
- **功能**：管理标签选项过滤逻辑
- **职责**：
  - 控制启用/未启用标签的显示策略
  - 处理标签选择变化
  - 维护标签状态
- **需要修改**：`extractTagsFromFields()` 调用逻辑，确保只处理最后一行标签

#### 2. `useTagManagement.ts` - 标签管理核心逻辑
- **关键方法**：
  - `extractTagsFromFields()` - 从文本字段提取标签 **[需要修改]**
  - `handleTagAddition()` - 处理标签添加 **[需要修改]**
  - `handleTagRemoval()` - 处理标签删除 **[需要修改]**
  - `processTagSelectionChanges()` - 处理标签选择变化
- **修改重点**：修改标签提取和操作逻辑，只处理最后一行

#### 3. `tagParsing.ts` - 标签解析工具
- **核心函数**：
  - `parseTagsWithOriginalFormat()` - 解析标签并保持原格式 **[需要修改]**
  - `formatTextWithTags()` - 格式化文本标签 **[需要修改]**
  - `getTagCodesByIds()` - 根据ID获取标签代码
- **修改重点**：添加只解析最后一行标签的功能

#### 4. `useExtractTags.ts` - 标签提取hook
- **功能**：从文本内容提取标签并设置到表单
- **当前问题**：使用 `/\[([^\]]*)\]$/` 正则表达式，已经只匹配最后一行
- **状态**：可能不需要修改，需要验证

### 数据流说明

```
用户操作标签选择器 
    ↓
TagsContext.addTags() 
    ↓  
useTagManagement.processTagSelectionChanges()
    ↓
handleTagAddition/handleTagRemoval()
    ↓
formatTextWithTags() - 更新文本内容
    ↓
表单字段更新(body/summary)
```

### 关键数据结构

```typescript
// 标签接口
interface Tag {
  id: number;
  code: string;
  name: string;
  isEnabled: boolean;  // 核心字段：标签是否启用
}

// 内容标签接口
interface IContentTag {
  id: number;
  name: string;
}

// 解析标签接口
interface IParsedTag {
  tag: string;      // 大写标签代码
  original: string; // 原始格式
}
```

## 实现策略

### 1. 修改标签解析逻辑
在 `tagParsing.ts` 中添加新函数：
```typescript
// 只解析最后一行的标签
export const parseLastLineTagsOnly = (text: string): IParsedTag[] => {
  // 实现逻辑：分割文本为行，只处理最后一行
}

// 只更新最后一行的标签
export const formatLastLineWithTags = (
  text: string,
  tagCodes: string[],
  originalFormats: Record<string, string>
): string => {
  // 实现逻辑：保持前面行不变，只修改最后一行
}
```

### 2. 修改标签管理逻辑
在 `useTagManagement.ts` 中：
- 修改 `extractTagsFromFields()` 使用新的解析函数
- 修改 `handleTagAddition()` 和 `handleTagRemoval()` 使用新的格式化函数

### 3. 验证现有逻辑
检查 `useExtractTags.ts` 是否已经正确实现了最后一行匹配

## 测试计划

### 1. 单元测试
- 测试新的 `parseLastLineTagsOnly()` 函数
- 测试新的 `formatLastLineWithTags()` 函数
- 验证多行文本处理的正确性

### 2. 集成测试
- 测试标签选择器添加标签的行为
- 测试标签选择器删除标签的行为
- 测试混合场景（有效/无效标签混合）

### 3. 用户体验测试
- 验证前面行的标签不被影响
- 验证标签格式保持不变
- 验证只有启用的标签参与处理

## 验收标准

### ✅ 功能验收标准
1. **标签解析范围**：系统只处理文本最后一行的标签
2. **标签过滤**：只处理 `isEnabled=true` 的标签
3. **前行保护**：文本前面行的标签保持不变
4. **格式保持**：标签的原始格式（大小写、间距）得到保持

### ✅ 行为验收标准
1. **添加标签**：新标签只出现在最后一行
2. **删除标签**：只从最后一行删除标签
3. **无效标签**：未启用的标签不被处理
4. **混合内容**：包含多行标签的文本正确处理

### ✅ 技术验收标准
1. **代码质量**：新代码符合项目编码规范
2. **测试覆盖**：核心逻辑有完整的单元测试
3. **性能影响**：修改不影响现有功能的性能
4. **向后兼容**：不破坏现有的标签功能