# 标签系统优化需求文档

## 背景

在 TNO 编辑器系统中，内容编辑功能的标签处理存在问题，需要优化标签系统的行为逻辑。

## 核心问题

在 `@/editor` 编辑器中编辑 content 时，点击标签(tag)会出现以下问题：
- 标签系统将所有标签"整体集合到一起"
- 包括未启用(disabled)的标签和无效标签被错误处理
- 导致数据混乱和用户体验问题

## 具体需求

### 1. 标签过滤需求
- **目标**：只处理已启用最后一行的标签

### 3. 范围限制需求
- **目标**：只处理文本的最后一行标签
- **行为**：前面所有行的标签（包括无效标签）应保持原位不动
- **业务逻辑**："只在最后一行聚合已经激活的标签"

### 4. 期望行为示例

**原始文本：**
```
bb[a]
cc [c]  
time [ICBC,BC]
```

**期望结果：**
- `[a]` 和 `[c]` 保持在原位置不被处理
- 只有最后一行的 `[ICBC,BC]` 参与标签系统管理
- 新添加的标签只会出现在最后一行添加和删除

## 技术实现

### 项目目录结构

```
tno/
├── app/editor/src/features/content/form/
│   ├── components/tags/                    # 标签组件目录
│   │   ├── TagsContext.tsx                # 标签上下文管理（主要修改文件）
│   │   ├── Tags.tsx                       # 标签UI组件
│   │   ├── DraggableTagList.tsx          # 可拖拽标签列表
│   │   ├── hooks/
│   │   │   └── useTagManagement.ts       # 标签管理逻辑（重要修改文件）
│   │   ├── utils/
│   │   │   └── tagParsing.ts             # 标签解析工具（重要修改文件）
│   │   └── types.ts                      # 标签类型定义
│   ├── hooks/
│   │   └── useExtractTags.ts             # 标签提取hook（需修改）
│   ├── ContentForm.tsx                   # 主内容表单
│   ├── ContentStoryForm.tsx              # 内容故事表单
│   └── ContentTranscriptForm.tsx         # 内容转录表单
```

### 核心文件说明

#### 1. `TagsContext.tsx` - 标签上下文管理
- **功能**：管理标签选项过滤逻辑
- **职责**：
  - 控制启用/未启用标签的显示策略
  - 处理标签选择变化
  - 维护标签状态

#### 2. `useTagManagement.ts` - 标签管理核心逻辑  
- **关键方法**：
  - `extractTagsFromFields()` - 从文本字段提取标签
  - `handleTagAddition()` - 处理标签添加
  - `handleTagRemoval()` - 处理标签删除
  - `processTagSelectionChanges()` - 处理标签选择变化

#### 3. `tagParsing.ts` - 标签解析工具
- **核心函数**：
  - `parseTagsWithOriginalFormat()` - 解析标签并保持原格式
  - `formatTextWithTags()` - 格式化文本标签
  - `getTagCodesByIds()` - 根据ID获取标签代码

#### 4. `useExtractTags.ts` - 标签提取hook
- **功能**：从文本内容提取标签并设置到表单

### 数据流说明

```
用户操作标签选择器 
    ↓
TagsContext.addTags() 
    ↓  
useTagManagement.processTagSelectionChanges()
    ↓
handleTagAddition/handleTagRemoval()
    ↓
formatTextWithTags() - 更新文本内容
    ↓
表单字段更新(body/summary)
```

### 关键数据结构

```typescript
// 标签接口
interface Tag {
  id: number;
  code: string;
  name: string;
  isEnabled: boolean;  // 核心字段：标签是否启用
}

// 内容标签接口  
interface IContentTag {
  id: number;
  name: string;
}
```